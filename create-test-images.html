<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>创建测试图片</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .canvas-container {
            margin: 20px 0;
            text-align: center;
        }
        canvas {
            border: 1px solid #ccc;
            margin: 10px;
        }
        button {
            background: #007bff;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 4px;
            cursor: pointer;
            margin: 5px;
        }
        button:hover {
            background: #0056b3;
        }
        .size-info {
            font-size: 14px;
            color: #666;
            margin-top: 5px;
        }
    </style>
</head>
<body>
    <h1>创建测试图片</h1>
    <p>这些图片可以用来测试圆形裁剪的最小展示尺寸功能：</p>

    <div class="canvas-container">
        <h3>极小图片 (50×50)</h3>
        <canvas id="canvas50" width="50" height="50"></canvas>
        <div class="size-info">应该被放大到最小尺寸</div>
        <button onclick="downloadCanvas('canvas50', 'test-50x50.png')">下载</button>
    </div>

    <div class="canvas-container">
        <h3>小图片 (100×100)</h3>
        <canvas id="canvas100" width="100" height="100"></canvas>
        <div class="size-info">应该被放大到最小尺寸</div>
        <button onclick="downloadCanvas('canvas100', 'test-100x100.png')">下载</button>
    </div>

    <div class="canvas-container">
        <h3>中等图片 (200×200)</h3>
        <canvas id="canvas200" width="200" height="200"></canvas>
        <div class="size-info">可能被放大到最小尺寸</div>
        <button onclick="downloadCanvas('canvas200', 'test-200x200.png')">下载</button>
    </div>

    <div class="canvas-container">
        <h3>大图片 (800×800)</h3>
        <canvas id="canvas800" width="800" height="800"></canvas>
        <div class="size-info">应该被缩小显示</div>
        <button onclick="downloadCanvas('canvas800', 'test-800x800.png')">下载</button>
    </div>

    <script>
        function createTestImage(canvasId, size) {
            const canvas = document.getElementById(canvasId);
            const ctx = canvas.getContext('2d');
            
            // 创建渐变背景
            const gradient = ctx.createRadialGradient(size/2, size/2, 0, size/2, size/2, size/2);
            gradient.addColorStop(0, '#ff6b6b');
            gradient.addColorStop(0.5, '#4ecdc4');
            gradient.addColorStop(1, '#45b7d1');
            
            ctx.fillStyle = gradient;
            ctx.fillRect(0, 0, size, size);
            
            // 添加文字
            ctx.fillStyle = 'white';
            ctx.font = `${Math.max(12, size/8)}px Arial`;
            ctx.textAlign = 'center';
            ctx.textBaseline = 'middle';
            ctx.fillText(`${size}×${size}`, size/2, size/2);
            
            // 添加边框
            ctx.strokeStyle = '#333';
            ctx.lineWidth = 2;
            ctx.strokeRect(1, 1, size-2, size-2);
        }

        function downloadCanvas(canvasId, filename) {
            const canvas = document.getElementById(canvasId);
            const link = document.createElement('a');
            link.download = filename;
            link.href = canvas.toDataURL();
            link.click();
        }

        // 创建所有测试图片
        createTestImage('canvas50', 50);
        createTestImage('canvas100', 100);
        createTestImage('canvas200', 200);
        createTestImage('canvas800', 800);
    </script>
</body>
</html>

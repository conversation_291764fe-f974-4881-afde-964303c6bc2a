"use client";

import React, { useState, useRef, useCallback } from 'react';
import ReactCrop, { Crop, PixelCrop, centerCrop, makeAspectCrop } from 'react-image-crop';
import ConfettiExplosion from 'react-confetti-explosion';
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card';
import { Button } from '@/components/ui/button';
import { Upload, Download } from 'lucide-react';
import { cn } from '@/lib/utils';
import { useTranslations } from 'next-intl';
import 'react-image-crop/dist/ReactCrop.css';

interface CircleCropProps {
  className?: string;
}

// 最小展示尺寸配置
const MIN_DISPLAY_SIZE = {
  desktop: { width: 500, height: 500 },
  mobile: { width: 250, height: 250 }
};

// 相对最小尺寸比例（基于视窗）
const MIN_DISPLAY_RATIO = {
  desktop: { widthRatio: 0.3, heightRatio: 0.35 },
  mobile: { widthRatio: 0.7, heightRatio: 0.4 }
};

// 设备检测函数
const isMobileDevice = () => {
  if (typeof window === 'undefined') return false;
  return window.innerWidth <= 768; // 768px 作为移动端断点
};

export default function CircleCrop({ className }: CircleCropProps) {
  const t = useTranslations('circle_crop');
  const fileInputRef = useRef<HTMLInputElement>(null);
  const imgRef = useRef<HTMLImageElement>(null);
  
  const [image, setImage] = useState<string>('');
  const [crop, setCrop] = useState<Crop>();
  const [completedCrop, setCompletedCrop] = useState<PixelCrop>();
  const [isUploading, setIsUploading] = useState(false);
  const [isProcessing, setIsProcessing] = useState(false);
  const [error, setError] = useState<string>('');
  const [isDragOver, setIsDragOver] = useState(false);
  const [imageDisplaySize, setImageDisplaySize] = useState<{width: number, height: number}>({width: 0, height: 0});
  const [scale, setScale] = useState<number>(1);
  const [isEnlarged, setIsEnlarged] = useState<boolean>(false);
  const [showConfetti, setShowConfetti] = useState(false);

  // 支持的文件类型
  const supportedTypes = ['image/jpeg', 'image/jpg', 'image/png', 'image/gif', 'image/webp'];
  const maxFileSize = 10 * 1024 * 1024; // 10MB

  // 计算合适的显示尺寸
  const calculateDisplaySize = useCallback((naturalWidth: number, naturalHeight: number) => {
    if (typeof window === 'undefined') {
      return {
        width: Math.min(naturalWidth, 800),
        height: Math.min(naturalHeight, 600),
        scale: 1,
        isEnlarged: false
      };
    }

    const isMobile = isMobileDevice();
    const config = isMobile ? MIN_DISPLAY_SIZE.mobile : MIN_DISPLAY_SIZE.desktop;
    const ratioConfig = isMobile ? MIN_DISPLAY_RATIO.mobile : MIN_DISPLAY_RATIO.desktop;

    // 获取容器的最大可用空间（增加最大限制以支持小图片放大）
    const maxWidth = Math.min(window.innerWidth * 0.9, 1200);
    const maxHeight = Math.min(window.innerHeight * 0.8, 900);

    // 计算相对最小尺寸
    const relativeMinWidth = window.innerWidth * ratioConfig.widthRatio;
    const relativeMinHeight = window.innerHeight * ratioConfig.heightRatio;

    // 确定最小尺寸要求（取绝对最小值和相对最小值的较大者）
    const minWidth = Math.max(config.width, relativeMinWidth);
    const minHeight = Math.max(config.height, relativeMinHeight);

    // 计算基于最小尺寸的缩放比例
    const minScaleX = minWidth / naturalWidth;
    const minScaleY = minHeight / naturalHeight;
    const minScale = Math.max(minScaleX, minScaleY); // 确保达到最小尺寸

    // 计算基于最大尺寸的缩放比例
    const maxScaleX = maxWidth / naturalWidth;
    const maxScaleY = maxHeight / naturalHeight;
    const maxScale = Math.min(maxScaleX, maxScaleY); // 容器限制的最大缩放

    // 最终缩放比例：优先满足最小尺寸，但不超过容器限制和3倍放大限制
    let finalScale = minScale;
    if (finalScale > maxScale) {
      // 如果最小尺寸要求超过了容器限制，使用容器限制
      finalScale = maxScale;
    }
    // 限制最大放大倍数为3倍
    finalScale = Math.min(finalScale, 3);

    const finalWidth = Math.round(naturalWidth * finalScale);
    const finalHeight = Math.round(naturalHeight * finalScale);

    // 调试信息（开发环境下输出）
    if (process.env.NODE_ENV === 'development') {
      console.log('图片尺寸计算:', {
        原始尺寸: `${naturalWidth}×${naturalHeight}`,
        设备类型: isMobile ? '移动端' : '桌面端',
        最小尺寸要求: `${minWidth}×${minHeight}`,
        最小缩放比例: minScale.toFixed(2),
        最大缩放比例: maxScale.toFixed(2),
        最终缩放比例: finalScale.toFixed(2),
        最终显示尺寸: `${finalWidth}×${finalHeight}`,
        是否被放大: finalScale > 1
      });
    }

    return {
      width: finalWidth,
      height: finalHeight,
      scale: finalScale,
      isEnlarged: finalScale > 1 // 标记是否被放大
    };
  }, []);

  // 验证裁剪坐标是否有效
  const validateCropCoordinates = useCallback((crop: PixelCrop, imageWidth: number, imageHeight: number) => {
    return (
      crop.x >= 0 &&
      crop.y >= 0 &&
      crop.x + crop.width <= imageWidth &&
      crop.y + crop.height <= imageHeight &&
      crop.width > 0 &&
      crop.height > 0
    );
  }, []);

  const validateFile = (file: File): boolean => {
    if (!supportedTypes.includes(file.type)) {
      setError(t('error_file_type'));
      return false;
    }
    if (file.size > maxFileSize) {
      setError(t('error_file_size'));
      return false;
    }
    return true;
  };

  const handleFileUpload = useCallback((file: File) => {
    if (!validateFile(file)) return;

    setIsUploading(true);
    setError('');

    const reader = new FileReader();
    reader.onload = () => {
      setImage(reader.result as string);
      setIsUploading(false);
    };
    reader.onerror = () => {
      setError(t('error_upload'));
      setIsUploading(false);
    };
    reader.readAsDataURL(file);
  }, [t]);

  const handleFileInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleDrop = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
    
    const file = e.dataTransfer.files[0];
    if (file) {
      handleFileUpload(file);
    }
  };

  const handleDragOver = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(true);
  };

  const handleDragLeave = (e: React.DragEvent<HTMLDivElement>) => {
    e.preventDefault();
    setIsDragOver(false);
  };

  const onImageLoad = (e: React.SyntheticEvent<HTMLImageElement>) => {
    const { naturalWidth, naturalHeight } = e.currentTarget;

    // 计算合适的显示尺寸
    const displaySize = calculateDisplaySize(naturalWidth, naturalHeight);
    setImageDisplaySize({ width: displaySize.width, height: displaySize.height });
    setScale(displaySize.scale);
    setIsEnlarged(displaySize.isEnlarged);

    // 创建一个正方形的裁剪区域，居中显示
    const crop = centerCrop(
      makeAspectCrop(
        {
          unit: '%',
          width: 80,
        },
        1, // 1:1 aspect ratio for circle
        naturalWidth,
        naturalHeight
      ),
      naturalWidth,
      naturalHeight
    );

    setCrop(crop);
  };

  const getCroppedImg = useCallback(async (): Promise<string | null> => {
    if (!completedCrop || !imgRef.current) return null;

    const image = imgRef.current;
    const canvas = document.createElement('canvas');
    const ctx = canvas.getContext('2d');

    if (!ctx) return null;

    // 验证裁剪坐标
    if (!validateCropCoordinates(completedCrop, image.naturalWidth, image.naturalHeight)) {
      console.error('Invalid crop coordinates:', completedCrop);
      return null;
    }

    const scaleX = image.naturalWidth / image.width;
    const scaleY = image.naturalHeight / image.height;

    // 设置canvas尺寸为裁剪区域的实际像素尺寸
    canvas.width = completedCrop.width;
    canvas.height = completedCrop.height;

    // 绘制裁剪的图片
    ctx.drawImage(
      image,
      completedCrop.x * scaleX,
      completedCrop.y * scaleY,
      completedCrop.width * scaleX,
      completedCrop.height * scaleY,
      0,
      0,
      completedCrop.width,
      completedCrop.height
    );

    // 创建圆形遮罩
    const radius = Math.min(completedCrop.width, completedCrop.height) / 2;
    const centerX = completedCrop.width / 2;
    const centerY = completedCrop.height / 2;

    // 获取图像数据
    const imageData = ctx.getImageData(0, 0, canvas.width, canvas.height);
    const data = imageData.data;

    // 应用圆形遮罩
    for (let y = 0; y < canvas.height; y++) {
      for (let x = 0; x < canvas.width; x++) {
        const distance = Math.sqrt((x - centerX) ** 2 + (y - centerY) ** 2);
        if (distance > radius) {
          const index = (y * canvas.width + x) * 4;
          data[index + 3] = 0; // 设置alpha为0（透明）
        }
      }
    }

    // 将修改后的图像数据放回canvas
    ctx.putImageData(imageData, 0, 0);

    return new Promise((resolve) => {
      canvas.toBlob((blob) => {
        if (blob) {
          resolve(URL.createObjectURL(blob));
        } else {
          resolve(null);
        }
      }, 'image/png');
    });
  }, [completedCrop]);

  const handleDownload = async () => {
    if (!completedCrop) return;

    setIsProcessing(true);
    setError('');

    try {
      const croppedImageUrl = await getCroppedImg();
      if (croppedImageUrl) {
        const link = document.createElement('a');
        link.download = 'circle-cropped-image.png';
        link.href = croppedImageUrl;
        link.click();
        URL.revokeObjectURL(croppedImageUrl);

        // 触发礼花特效
        setShowConfetti(true);
        // 3秒后自动隐藏礼花
        setTimeout(() => {
          setShowConfetti(false);
        }, 3000);
      } else {
        setError(t('error_crop'));
      }
    } catch (err) {
      setError(t('error_crop'));
    } finally {
      setIsProcessing(false);
    }
  };

  const handleUploadNew = () => {
    fileInputRef.current?.click();
  };

  const handleUploadClick = () => {
    fileInputRef.current?.click();
  };

  return (
    <div className={cn("w-full max-w-4xl mx-auto relative", className)} id='circle-crop'>
      {/* 礼花特效 */}
      {showConfetti && typeof window !== 'undefined' && (
        <div className="fixed inset-0 flex items-center justify-center pointer-events-none z-50">
          <ConfettiExplosion
            particleCount={80}
            duration={3000}
            colors={['#ff6b6b', '#4ecdc4', '#45b7d1', '#96ceb4', '#feca57', '#ff9ff3']}
            force={0.6}
            width={1200}
            height="120vh"
          />
        </div>
      )}
      <Card>
        {/* <CardHeader>
          <CardTitle className="text-center">{t('title')}</CardTitle>
        </CardHeader> */}
        <CardContent className="space-y-6">
          {/* 隐藏的文件输入框，在任何状态下都可用 */}
          <input
            ref={fileInputRef}
            type="file"
            accept="image/*"
            onChange={handleFileInputChange}
            className="hidden"
          />
          {!image ? (
            // 上传区域
            <div
              className={cn(
                "border-2 border-dashed rounded-lg p-8 text-center transition-colors",
                isDragOver ? "border-primary bg-primary/5" : "border-muted-foreground/25",
                "hover:border-primary hover:bg-primary/5 cursor-pointer"
              )}
              onDrop={handleDrop}
              onDragOver={handleDragOver}
              onDragLeave={handleDragLeave}
              onClick={handleUploadClick}
            >
              <Upload className="mx-auto h-12 w-12 text-muted-foreground mb-4" />
              <p className="text-lg font-medium mb-2">{t('upload_hint')}</p>
              <p className="text-sm text-muted-foreground mb-4">{t('supported_formats')}</p>
              <Button disabled={isUploading}>
                {isUploading ? t('processing') : t('upload_button')}
              </Button>
            </div>
          ) : (
            // 裁剪区域
            <div className="space-y-4">
              <div className="flex justify-center">
                <div className="relative w-full flex justify-center">
                  <ReactCrop
                    crop={crop}
                    onChange={(_, percentCrop) => setCrop(percentCrop)}
                    onComplete={(c) => setCompletedCrop(c)}
                    aspect={1}
                    circularCrop
                    keepSelection
                  >
                    <img
                      ref={imgRef}
                      src={image}
                      alt="Crop preview"
                      onLoad={onImageLoad}
                      style={{
                        width: imageDisplaySize.width || 'auto',
                        height: imageDisplaySize.height || 'auto',
                        maxWidth: '100%',
                        maxHeight: '80vh'
                      }}
                      className="object-contain"
                    />
                  </ReactCrop>
                </div>
              </div>

              {/* 缩放信息和控制 */}
              {scale !== 1 && (
                <div className="text-center text-sm text-muted-foreground">
                  {isEnlarged ? (
                    <p>{t('enlarged_for_operation')} {Math.round(scale * 100)}%</p>
                  ) : (
                    <p>{t('scaled_to')} {Math.round(scale * 100)}% {t('for_better_display')}</p>
                  )}
                </div>
              )}
              
              {/* 操作按钮 */}
              <div className="flex justify-center gap-4">
                <Button
                  variant="outline"
                  onClick={handleUploadNew}
                  className="flex items-center gap-2"
                >
                  <Upload className="h-4 w-4" />
                  {t('reset_button')}
                </Button>
                <Button
                  onClick={handleDownload}
                  disabled={!completedCrop || isProcessing}
                  className="flex items-center gap-2"
                >
                  <Download className="h-4 w-4" />
                  {isProcessing ? t('processing') : t('download_button')}
                </Button>
              </div>
            </div>
          )}

          {/* 错误提示 */}
          {error && (
            <div className="text-center text-sm text-destructive bg-destructive/10 p-3 rounded-md">
              {error}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

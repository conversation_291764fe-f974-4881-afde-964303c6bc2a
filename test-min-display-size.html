<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>测试最小展示尺寸功能</title>
    <style>
        body {
            font-family: Arial, sans-serif;
            max-width: 800px;
            margin: 0 auto;
            padding: 20px;
        }
        .test-section {
            margin: 20px 0;
            padding: 20px;
            border: 1px solid #ddd;
            border-radius: 8px;
        }
        .test-image {
            margin: 10px;
            border: 1px solid #ccc;
        }
        h2 {
            color: #333;
        }
        .description {
            color: #666;
            margin-bottom: 15px;
        }
    </style>
</head>
<body>
    <h1>圆形图片裁剪最小展示尺寸功能测试</h1>
    
    <div class="test-section">
        <h2>测试说明</h2>
        <p class="description">
            我们已经实现了圆形图片裁剪的最小展示尺寸功能。以下是测试要点：
        </p>
        <ul>
            <li><strong>桌面端最小尺寸</strong>：300×300 像素</li>
            <li><strong>移动端最小尺寸</strong>：250×250 像素</li>
            <li><strong>相对最小尺寸</strong>：桌面端视窗的25%×30%，移动端70%×40%</li>
            <li><strong>最大放大倍数</strong>：3倍（防止内存问题）</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试步骤</h2>
        <ol>
            <li>访问 <a href="http://localhost:3000/circle-crop-image" target="_blank">圆形裁剪页面</a></li>
            <li>上传一张小尺寸图片（如 100×100 像素）</li>
            <li>观察图片是否被放大到最小操作尺寸</li>
            <li>检查是否显示"已放大至 XXX%"的提示信息</li>
            <li>进行裁剪操作，确认操作流畅</li>
            <li>下载裁剪结果，验证质量是否基于原始图片</li>
        </ol>
    </div>

    <div class="test-section">
        <h2>预期行为</h2>
        <ul>
            <li>小图片会被自动放大到最小操作尺寸</li>
            <li>显示放大提示信息，如"已放大至 300%"</li>
            <li>大图片仍然按原有逻辑缩小显示</li>
            <li>最终裁剪结果基于原始图片像素，不受显示尺寸影响</li>
            <li>在移动端和桌面端都有合适的最小尺寸</li>
        </ul>
    </div>

    <div class="test-section">
        <h2>测试用例</h2>
        <h3>1. 极小图片测试</h3>
        <p>上传 50×50 像素的图片，应该被放大到最小尺寸</p>
        
        <h3>2. 小图片测试</h3>
        <p>上传 150×150 像素的图片，应该被放大到最小尺寸</p>
        
        <h3>3. 中等图片测试</h3>
        <p>上传 500×500 像素的图片，可能会被适当缩放</p>
        
        <h3>4. 大图片测试</h3>
        <p>上传 2000×2000 像素的图片，应该被缩小到合适尺寸</p>
        
        <h3>5. 响应式测试</h3>
        <p>调整浏览器窗口大小，观察最小尺寸是否相应调整</p>
    </div>

    <div class="test-section">
        <h2>功能验证清单</h2>
        <ul>
            <li>□ 小图片被正确放大到最小尺寸</li>
            <li>□ 显示正确的放大提示信息</li>
            <li>□ 大图片仍然正常缩小显示</li>
            <li>□ 裁剪操作流畅，选择框大小合适</li>
            <li>□ 最终下载的图片质量基于原始图片</li>
            <li>□ 移动端和桌面端都有合适的最小尺寸</li>
            <li>□ 没有性能问题或内存泄漏</li>
        </ul>
    </div>

    <script>
        // 简单的设备检测，与组件中的逻辑保持一致
        function isMobileDevice() {
            return window.innerWidth <= 768;
        }

        // 显示当前设备类型和窗口尺寸
        function updateDeviceInfo() {
            const deviceType = isMobileDevice() ? '移动端' : '桌面端';
            const windowSize = `${window.innerWidth}×${window.innerHeight}`;
            
            const infoDiv = document.getElementById('device-info') || document.createElement('div');
            infoDiv.id = 'device-info';
            infoDiv.innerHTML = `
                <strong>当前设备类型：</strong>${deviceType}<br>
                <strong>窗口尺寸：</strong>${windowSize}
            `;
            infoDiv.style.cssText = 'background: #f0f0f0; padding: 10px; margin: 10px 0; border-radius: 4px;';
            
            if (!document.getElementById('device-info')) {
                document.body.insertBefore(infoDiv, document.body.firstChild);
            }
        }

        // 页面加载时和窗口大小改变时更新设备信息
        updateDeviceInfo();
        window.addEventListener('resize', updateDeviceInfo);
    </script>
</body>
</html>
